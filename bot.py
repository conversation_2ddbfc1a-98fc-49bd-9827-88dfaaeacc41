import discord
import asyncio
import aiohttp
from datetime import datetime, timedelta
import pytz
from typing import Dict, Optional, Any
import os
import signal
import sys
from dotenv import load_dotenv
from enum import Enum

# Load environment variables
load_dotenv()

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
bot = discord.Bot(intents=intents)

# Constants
IRAN_TIMEZONE = pytz.timezone('Asia/Tehran')
GW2_API_BASE = "https://api.guildwars2.com/v2"
GW2_WORLD_BOSSES = f"{GW2_API_BASE}/worldbosses"
GW2_EVENTS = f"{GW2_API_BASE}/events"
GW2_MAPS = f"{GW2_API_BASE}/maps"

# Cache for boss data and countdown settings
boss_cache = {}
active_countdowns = {}  # {message_id: {'boss_id': str, 'channel_id': int, 'next_spawn': datetime}}
last_fetch = None
CACHE_DURATION = 300  # 5 minutes in seconds
COUNTDOWN_UPDATE_INTERVAL = 30  # seconds between countdown updates

class BossState(Enum):
    INACTIVE = "Inactive"
    WARMUP = "Warmup"
    ACTIVE = "Active"
    SUCCESS = "Success"
    FAIL = "Fail"

class WorldBoss:
    def __init__(self, boss_id: str, name: str, waypoint: str, location: str, event_id: str):
        self.id = boss_id
        self.name = name
        self.waypoint = waypoint
        self.location = location
        self.event_id = event_id
        self.state = BossState.INACTIVE
        self.next_spawn = None
        self.last_spawn = None
        self.notified_10min = False
        self.notified_5min = False
        self.notified_spawn = False

    def update_state(self, state: BossState):
        self.state = state
        
    def update_spawn_time(self, next_spawn: datetime):
        self.next_spawn = next_spawn
        self.notified_10min = False
        self.notified_5min = False
        self.notified_spawn = False
        self.countdown_message = None

# Known world bosses with their event IDs and waypoints
WORLD_BOSSES = {
    # Core Tyria World Bosses
    "shatterer": {
        "name": "The Shatterer",
        "waypoint": "[&BN4DAAA=]",
        "location": "Blazeridge Steppes",
        "event_id": "EED8A207-FE0B-4F3F-9222-9F79B199A216"
    },
    "tequatl": {
        "name": "Tequatl the Sunless",
        "waypoint": "[&Bk8FAAA=]",
        "location": "Sparksfly Fen",
        "event_id": "D31C6041-0D49-444B-93D3-2943B46816A9"
    },
    "claw_of_jormag": {
        "name": "Claw of Jormag",
        "waypoint": "[&BHMHAAA=]",
        "location": "Frostgorge Sound",
        "event_id": "E84FF6AC-68DA-4F42-B0B7-494B6B7C4EA0"
    },
    "megadestroyer": {
        "name": "The Megadestroyer",
        "waypoint": "[&BH4CAAA=]",
        "location": "Mount Maelstrom",
        "event_id": "A3D8A207-FE0B-4F3F-9222-9F79B199A216"
    },
    "golem_mark_ii": {
        "name": "Golem Mark II",
        "waypoint": "[&BEgDAAA=]",
        "location": "Mount Maelstrom",
        "event_id": "6D2F27C8-6E5B-4B3C-8E7D-9A2A3B9B7C6D"
    },
    "fire_elemental": {
        "name": "Fire Elemental",
        "waypoint": "[&BPcAAAA=]",
        "location": "Metrica Province",
        "event_id": "D3D2E9ED-45E4-4A70-9661-6166F0EFBD91"
    },
    "great_jungle_wurm": {
        "name": "Great Jungle Wurm",
        "waypoint": "[&BPgAAAA=]",
        "location": "Caledon Forest",
        "event_id": "1A2DEE29-9D92-4F97-8B2D-2E3D1F9F14E8"
    },
    "shadow_behemoth": {
        "name": "Shadow Behemoth",
        "waypoint": "[&BPcAAAA=]",
        "location": "Queensdale",
        "event_id": "F530006C-4F2F-4FCD-8E6D-2AA0B5B25E20"
    },
    "svanir_shaman_chief": {
        "name": "Svanir Shaman Chief",
        "waypoint": "[&BKgBAAA=]",
        "location": "Wayfarer Foothills",
        "event_id": "F530006C-4F2F-4FCD-8E6D-2AA0B5B25E21"
    },
    "taidha_covington": {
        "name": "Taidha Covington",
        "waypoint": "[&BKgCAAA=]",
        "location": "Bloodtide Coast",
        "event_id": "F530006C-4F2F-4FCD-8E6D-2AA0B5B25E22"
    },
    # HoT and PoF World Bosses
    "chak_gerent": {
        "name": "Chak Gerent",
        "waypoint": "[&BL0HAAA=]",
        "location": "Tangled Depths",
        "event_id": "A4B6C3D4-E5F6-47A8-B9C0-D1E2F3A4B5C6"
    },
    "death_branded_shatterer": {
        "name": "Death-Branded Shatterer",
        "waypoint": "[&BKgIAAA=]",
        "location": "Domain of Vabbi",
        "event_id": "B5C6D7E8-F9A0-41B2-C3D4-E5F647A8B9C0"
    },
    "drakkar": {
        "name": "Drakkar",
        "waypoint": "[&BLYJAAA=]",
        "location": "Bjora Marches",
        "event_id": "C6D7E8F9-0A1B-2C3D-4E5F-647A8B9C0D1E"
    },
    # Dragon Response Missions
    "dragonstorm": {
        "name": "Dragonstorm",
        "waypoint": "[&BLwJAAA=]",
        "location": "Eye of the North",
        "event_id": "D7E8F90A-1B2C-3D4E-5F64-7A8B9C0D1E2F"
    },
    # EoD World Bosses
    "soo_won": {
        "name": "Soo-Won",
        "waypoint": "[&BL0KAAA=]",
        "location": "Seitung Province",
        "event_id": "E8F90A1B-2C3D-4E5F-647A-8B9C0D1E2F30"
    },
    "aetherblade_assault": {
        "name": "Aetherblade Assault",
        "waypoint": "[&BL0LAAA=]",
        "location": "New Kaineng City",
        "event_id": "F90A1B2C-3D4E-5F64-7A8B-9C0D1E2F3041"
    },
    # Other Notable Bosses
    "karka_queen": {
        "name": "Karka Queen",
        "waypoint": "[&BKgGAAA=]",
        "location": "Southsun Cove",
        "event_id": "0A1B2C3D-4E5F-647A-8B9C-0D1E2F304152"
    },
    "marionette": {
        "name": "Twisted Marionette",
        "waypoint": "[&BL0MAAA=]",
        "location": "Lornar's Pass",
        "event_id": "1B2C3D4E-5F64-7A8B-9C0D-1E2F30415263"
    },
    "triple_trouble": {
        "name": "Triple Trouble (Tequatl's Abomination)",
        "waypoint": "[&BKgBAAA=]",
        "location": "Bloodtide Coast",
        "event_id": "2C3D4E5F-647A-8B9C-0D1E-2F3041526374"
    }
}

async def fetch_world_boss_schedule() -> Dict[str, Any]:
    """Fetch the world boss schedule from GW2 API"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{GW2_WORLD_BOSSES}") as response:
                if response.status == 200:
                    return await response.json()
                return {}
    except Exception as e:
        print(f"Error fetching world boss schedule: {e}")
        return {}

async def fetch_event_state(event_id: str) -> Dict[str, Any]:
    """Fetch the current state of a specific event"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{GW2_EVENTS}/{event_id}") as response:
                if response.status == 200:
                    return await response.json()
                return {}
    except Exception as e:
        print(f"Error fetching event state: {e}")
        return {}

def calculate_next_spawn(boss_name: str) -> Optional[datetime]:
    """Calculate the next spawn time for a boss based on current time"""
    now = datetime.now(IRAN_TIMEZONE)
    
    # This is a simplified calculation - in a real implementation, you'd want to
    # use the actual schedule from the API or a more sophisticated calculation
    # based on the boss's known spawn patterns
    
    # For now, we'll use a simple 2-hour rotation for demonstration
    next_spawn = now.replace(second=0, microsecond=0)
    next_spawn = next_spawn + timedelta(hours=2 - (now.hour % 2), minutes=0)
    return next_spawn

async def check_boss_timers():
    """Check boss timers and send notifications"""
    global last_fetch, boss_cache
    
    now = datetime.now(IRAN_TIMEZONE)
    
    # Only fetch from API if cache is expired
    if not last_fetch or (now - last_fetch).total_seconds() > CACHE_DURATION:
        for boss_id, boss_data in WORLD_BOSSES.items():
            event_data = await fetch_event_state(boss_data["event_id"])
            
            if event_data and "state" in event_data:
                if boss_id not in boss_cache:
                    boss_cache[boss_id] = WorldBoss(
                        boss_id,
                        boss_data["name"],
                        boss_data["waypoint"],
                        boss_data["location"],
                        boss_data["event_id"]
                    )
                
                boss = boss_cache[boss_id]
                boss.update_state(BossState(event_data["state"]))
                
                # If boss is inactive, calculate next spawn
                if boss.state == BossState.INACTIVE and not boss.next_spawn:
                    next_spawn = calculate_next_spawn(boss_id)
                    boss.update_spawn_time(next_spawn)
        
        last_fetch = now
    
    # Check for notifications
    for boss_id, boss in list(boss_cache.items()):
        if boss.next_spawn:
            time_until = (boss.next_spawn - now).total_seconds()
            minutes_until = int(time_until / 60)
            
            # 10-minute warning
            if 600 <= time_until < 660 and not boss.notified_10min:
                for guild in bot.guilds:
                    for channel in guild.text_channels:
                        if channel.permissions_for(guild.me).send_messages:
                            await send_boss_notification(boss, channel, 10)
                            boss.notified_10min = True
                            break
            
            # 5-minute warning
            elif 300 <= time_until < 360 and not boss.notified_5min:
                for guild in bot.guilds:
                    for channel in guild.text_channels:
                        if channel.permissions_for(guild.me).send_messages:
                            await send_boss_notification(boss, channel, 5)
                            boss.notified_5min = True
                            break
            
            # Spawn time
            elif 0 <= time_until < 60 and not boss.notified_spawn:
                for guild in bot.guilds:
                    for channel in guild.text_channels:
                        if channel.permissions_for(guild.me).send_messages:
                            await send_boss_notification(boss, channel, 0)
                            boss.notified_spawn = True
                            break
                
                # Schedule next spawn
                next_spawn = calculate_next_spawn(boss_id)
                boss.update_spawn_time(next_spawn)
                
                # Clean up countdown messages for this boss
                for msg_id in list(active_countdowns.keys()):
                    if active_countdowns[msg_id]['boss_id'] == boss_id:
                        del active_countdowns[msg_id]

async def update_countdown_messages():
    """Update all active countdown messages"""
    now = datetime.now(IRAN_TIMEZONE)
    
    for message_id, data in list(active_countdowns.items()):
        try:
            channel = bot.get_channel(data['channel_id'])
            if not channel:
                del active_countdowns[message_id]
                continue
                
            message = await channel.fetch_message(message_id)
            if not message:
                del active_countdowns[message_id]
                continue
                
            boss_id = data['boss_id']
            boss = boss_cache.get(boss_id)
            
            if not boss or not boss.next_spawn or (boss.next_spawn - now).total_seconds() <= 0:
                try:
                    await message.delete()
                except:
                    pass
                del active_countdowns[message_id]
                continue
                
            minutes_until = int((boss.next_spawn - now).total_seconds() / 60)
            embed = create_boss_embed(boss, minutes_until)
            await message.edit(embed=embed)
            
        except Exception as e:
            print(f"Error updating countdown: {e}")
            del active_countdowns[message_id]

async def countdown_loop():
    """Background task to update countdown messages"""
    while True:
        try:
            await update_countdown_messages()
        except Exception as e:
            print(f"Error in countdown loop: {e}")
        
        await asyncio.sleep(COUNTDOWN_UPDATE_INTERVAL)

async def send_boss_notification(boss: WorldBoss, channel: discord.TextChannel, minutes_until: int):
    """Send a notification about an upcoming boss spawn"""
    embed = create_boss_embed(boss, minutes_until)
    message = await channel.send(embed=embed)
    
    # If this is a countdown notification (not the spawn notification)
    if minutes_until > 0:
        active_countdowns[message.id] = {
            'boss_id': boss.id,
            'channel_id': channel.id,
            'next_spawn': boss.next_spawn
        }
        
        # Start the countdown loop if not running
        if not hasattr(bot, 'countdown_task') or bot.countdown_task.done():
            bot.countdown_task = bot.loop.create_task(countdown_loop())
    
    return message

def create_boss_embed(boss: WorldBoss, minutes_until: int) -> discord.Embed:
    """Create an embed for boss notifications with countdown"""
    now = datetime.now(IRAN_TIMEZONE)
    
    # Calculate time until spawn
    countdown_str = "Now!" if minutes_until <= 0 else ""
    if boss.next_spawn and minutes_until > 0:
        time_until = boss.next_spawn - now
        hours, remainder = divmod(int(time_until.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        countdown_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        
        # Create progress bar
        progress = 1 - (time_until.total_seconds() / 3600)  # Max 1 hour countdown
        progress = max(0, min(1, progress))
        progress_bar_length = 20
        filled_length = int(progress * progress_bar_length)
        progress_bar = '█' * filled_length + '░' * (progress_bar_length - filled_length)
        countdown_str = f"{countdown_str}\n`{progress_bar}`"
    
    color = 0x00ff00 if minutes_until == 0 else 0xffa500
    title = f"🕒 {boss.name} " + ("has spawned!" if minutes_until == 0 else f"in {minutes_until} minutes!")
    
    embed = discord.Embed(title=title, color=color)
    embed.add_field(name="📍 Location", value=f"{boss.location} {boss.waypoint}", inline=False)
    
    if minutes_until > 0 and boss.next_spawn:
        next_spawn_str = f"<t:{int(boss.next_spawn.timestamp())}:t>"
        embed.add_field(name="⏰ Spawn Time", value=f"{next_spawn_str} IRST", inline=False)
        if countdown_str:
            embed.add_field(name="Countdown", value=countdown_str, inline=False)
    elif boss.next_spawn:
        embed.add_field(name="⚔️ Status", value="Boss is up! Good luck!", inline=False)
    
    # Add tips based on boss
    tips = get_boss_tips(boss.id)
    if tips:
        embed.add_field(name="💡 Tips", value=tips, inline=False)
    
    embed.set_footer(text="Times are in IRST (Iran Standard Time)")
    return embed

def get_boss_tips(boss_id: str) -> str:
    """Get tips for a specific boss"""
    tips = {
        # Core Tyria Bosses
        "shatterer": (
            "• Bring CC for the breakbar\n"
            "• Stay spread during the crystal phase\n"
            "• Watch for the tail swipe attack"
        ),
        "tequatl": (
            "• Defend the turrets\n"
            "• Kill the hordes quickly\n"
            "• Stack on the boss during burn phase"
        ),
        "claw_of_jormag": (
            "• Destroy the ice walls\n"
            "• Watch for the ice spikes\n"
            "• Bring condition cleanses"
        ),
        "megadestroyer": (
            "• Avoid the lava pools\n"
            "• Break the breakbar quickly\n"
            "• Bring projectile reflects"
        ),
        "golem_mark_ii": (
            "• Avoid the laser beams\n"
            "• Destroy the power cores\n"
            "• Bring stability for the knockbacks"
        ),
        "fire_elemental": (
            "• Bring condition cleanses for burning\n"
            "• Stay mobile to avoid fire patches\n"
            "• Ranged attacks work well"
        ),
        "great_jungle_wurm": (
            "• Split into three groups for the heads\n"
            "• Watch for the poison fields\n"
            "• Bring condition cleanses"
        ),
        "shadow_behemoth": (
            "• Close the portals quickly\n"
            "• Watch for the shadow rifts\n"
            "• Focus on the hands when they appear"
        ),
        "svanir_shaman_chief": (
            "• Stay mobile to avoid ice patches\n"
            "• Bring stability for the knockbacks\n"
            "• Focus on the shaman first"
        ),
        "taidha_covington": (
            "• Watch for the cannon barrage\n"
            "• Stay mobile to avoid AoE attacks\n"
            "• Bring condition cleanses for bleeding"
        ),
        # HoT and PoF Bosses
        "chak_gerent": (
            "• Split into four lanes\n"
            "• CC the boss when the breakbar appears\n"
            "• Watch for the Chak Acid"
        ),
        "death_branded_shatterer": (
            "• Avoid the Branded zones\n"
            "• Bring condition cleanses\n"
            "• Watch for the tail swipe"
        ),
        "drakkar": (
            "• Break the defiance bar quickly\n"
            "• Avoid the ice fields\n"
            "• Bring condition cleanses for chill"
        ),
        "dragonstorm": (
            "• Split into two groups for the dragons\n"
            "• Use the special action key\n"
            "• Watch for the shared agony mechanic"
        ),
        # EoD Bosses
        "soo_won": (
            "• Split into groups for the tail and head\n"
            "• Use the jade bot protocols\n"
            "• Watch for the tsunami mechanic"
        ),
        "aetherblade_assault": (
            "• Focus on the snipers first\n"
            "• Avoid the red circles\n"
            "• Bring stability for the knockbacks"
        ),
        # Other Notable Bosses
        "karka_queen": (
            "• Avoid the rolling karka\n"
            "• Watch for the acid spray\n"
            "• Bring condition cleanses for bleeding"
        ),
        "marionette": (
            "• Split into five groups for the platforms\n"
            "• CC the champions quickly\n"
            "• Watch for the laser beams"
        ),
        "triple_trouble": (
            "• Requires organization with multiple squads\n"
            "• Each wurm head has different mechanics\n"
            "• Watch for the egg sacs and husks"
        )
    }
    return tips.get(boss_id, "• Stay alert and work together!\n• Bring appropriate boons and conditions\n• Watch for mechanics and communicate with your group")

@bot.event
async def on_ready():
    """Bot startup event with enhanced status messages"""
    current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S IRST")

    print("=" * 60)
    print("🤖 GUILD WARS 2 WORLD BOSS BOT STARTING UP")
    print("=" * 60)
    print(f"📅 Startup Time: {current_time}")
    print(f"🔗 Bot Name: {bot.user.name}")
    print(f"🆔 Bot ID: {bot.user.id}")
    print(f"🌐 Connected to {len(bot.guilds)} guild(s)")

    # Handle command cleanup and syncing
    cleanup_commands = os.getenv('CLEANUP_COMMANDS', 'false').lower() == 'true'

    if cleanup_commands:
        print("🧹 CLEANUP_COMMANDS=true detected - Performing full command cleanup...")
        try:
            # Clear all existing commands first
            print("🗑️  Clearing all existing slash commands...")
            await bot.sync_commands(commands=[])
            print("✅ All existing commands cleared successfully")

            # Small delay to ensure cleanup is processed
            await asyncio.sleep(3)

            # Now sync the new commands with force flag
            print("🔄 Registering fresh slash commands...")
            synced = await bot.sync_commands(force=True)

            # Get the actual registered commands
            registered_commands = bot.pending_application_commands
            if registered_commands:
                print(f"✅ Successfully registered {len(registered_commands)} fresh command(s)")
                for cmd in registered_commands:
                    print(f"   • /{cmd.name}: {cmd.description}")
            else:
                print("⚠️  No commands were found to register")

            print("💡 TIP: Set CLEANUP_COMMANDS=false in .env to skip cleanup on next restart")

        except Exception as e:
            print(f"❌ Failed during command cleanup: {e}")
            print("⚠️  Bot will continue running, but commands may not work properly")
    else:
        print("🔄 Syncing slash commands (normal mode)...")
        try:
            synced = await bot.sync_commands()
            if synced:
                print(f"✅ Successfully synced {len(synced)} command(s)")
                for cmd in synced:
                    print(f"   • /{cmd.name}: {cmd.description}")
            else:
                print("✅ Commands are already up to date")
        except Exception as e:
            print(f"❌ Failed to sync commands: {e}")
            print("⚠️  Bot will continue running, but commands may not work properly")

    # Start the boss timer loop and countdown loop
    print("⏰ Starting boss timer monitoring...")
    bot.loop.create_task(boss_timer_loop())

    # Start countdown loop if not already running
    if not hasattr(bot, 'countdown_task') or bot.countdown_task.done():
        bot.countdown_task = bot.loop.create_task(countdown_loop())

    print("🎯 Boss notification system active")
    print("=" * 60)
    print("✅ BOT IS NOW ONLINE AND READY!")
    print("=" * 60)

async def boss_timer_loop():
    """Main loop for checking boss timers"""
    await bot.wait_until_ready()
    
    while not bot.is_closed():
        try:
            await check_boss_timers()
        except Exception as e:
            print(f"Error in boss timer loop: {e}")
        
        await asyncio.sleep(30)  # Check every 30 seconds

@bot.slash_command(name="help", description="Show available commands and bot information")
async def help_command(ctx):
    """Display help information about the bot and its commands"""
    embed = discord.Embed(
        title="🤖 Guild Wars 2 World Boss Bot Help",
        description="Track world boss spawn times and get notifications!",
        color=0x00ff00
    )

    embed.add_field(
        name="📋 Available Commands",
        value=(
            "`/help` - Show this help message\n"
            "`/next_boss <boss_name>` - Check when a specific boss spawns next\n"
            "`/boss_list` - Show all available world bosses"
        ),
        inline=False
    )

    embed.add_field(
        name="🔔 Automatic Notifications",
        value=(
            "• 10-minute warning before boss spawn\n"
            "• 5-minute warning before boss spawn\n"
            "• Spawn notification when boss is up\n"
            "• Live countdown timers with progress bars"
        ),
        inline=False
    )

    embed.add_field(
        name="🌍 Supported Bosses",
        value=(
            "Core Tyria, Heart of Thorns, Path of Fire, "
            "End of Dragons, and special event bosses"
        ),
        inline=False
    )

    embed.add_field(
        name="⏰ Timezone",
        value="All times are displayed in IRST (Iran Standard Time)",
        inline=False
    )

    embed.set_footer(text="Bot created for Guild Wars 2 players • Times in IRST")
    await ctx.respond(embed=embed)

@bot.slash_command(name="boss_list", description="Show all available world bosses")
async def boss_list(ctx):
    """Display a list of all trackable world bosses"""
    embed = discord.Embed(
        title="🐉 Available World Bosses",
        description="Use `/next_boss <boss_name>` to check spawn times",
        color=0x0099ff
    )

    # Group bosses by category
    core_bosses = []
    expansion_bosses = []
    special_bosses = []

    for boss_id, boss_data in WORLD_BOSSES.items():
        boss_name = boss_data["name"]
        location = boss_data["location"]

        if boss_id in ["shatterer", "tequatl", "claw_of_jormag", "megadestroyer",
                       "golem_mark_ii", "fire_elemental", "great_jungle_wurm",
                       "shadow_behemoth", "svanir_shaman_chief", "taidha_covington"]:
            core_bosses.append(f"• **{boss_name}** - {location}")
        elif boss_id in ["chak_gerent", "death_branded_shatterer", "drakkar",
                         "dragonstorm", "soo_won", "aetherblade_assault"]:
            expansion_bosses.append(f"• **{boss_name}** - {location}")
        else:
            special_bosses.append(f"• **{boss_name}** - {location}")

    if core_bosses:
        embed.add_field(
            name="🏔️ Core Tyria Bosses",
            value="\n".join(core_bosses[:10]),  # Limit to avoid embed size issues
            inline=False
        )

    if expansion_bosses:
        embed.add_field(
            name="🌟 Expansion Bosses",
            value="\n".join(expansion_bosses),
            inline=False
        )

    if special_bosses:
        embed.add_field(
            name="🎯 Special Event Bosses",
            value="\n".join(special_bosses),
            inline=False
        )

    embed.add_field(
        name="💡 Usage Example",
        value="`/next_boss tequatl` or `/next_boss shatterer`",
        inline=False
    )

    embed.set_footer(text=f"Total: {len(WORLD_BOSSES)} trackable bosses")
    await ctx.respond(embed=embed)

@bot.slash_command(name="next_boss", description="Check when a world boss will spawn next")
async def next_boss(ctx, boss_name: str):
    """Get information about when a boss will spawn next"""
    boss_name = boss_name.lower().replace(" ", "_").replace("-", "_")

    if boss_name not in WORLD_BOSSES:
        # Try to find a partial match
        matches = [boss for boss in WORLD_BOSSES.keys() if boss_name in boss or boss in boss_name]
        if matches:
            boss_name = matches[0]
        else:
            embed = discord.Embed(
                title="❌ Boss Not Found",
                description=f"Could not find boss: `{boss_name}`",
                color=0xff0000
            )
            embed.add_field(
                name="💡 Tip",
                value="Use `/boss_list` to see all available bosses",
                inline=False
            )
            await ctx.respond(embed=embed)
            return

    boss_data = WORLD_BOSSES[boss_name]
    boss = boss_cache.get(boss_name)

    if not boss or not boss.next_spawn:
        next_spawn = calculate_next_spawn(boss_name)
        if not boss:
            boss = WorldBoss(
                boss_name,
                boss_data["name"],
                boss_data["waypoint"],
                boss_data["location"],
                boss_data["event_id"]
            )
            boss_cache[boss_name] = boss
        boss.update_spawn_time(next_spawn)

    embed = create_boss_embed(boss, int((boss.next_spawn - datetime.now(IRAN_TIMEZONE)).total_seconds() / 60))
    await ctx.respond(embed=embed)

def signal_handler(signum=None, frame=None):
    """Handle shutdown signals gracefully"""
    current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S IRST")
    print("\n" + "=" * 60)
    print("🛑 SHUTDOWN SIGNAL RECEIVED")
    print("=" * 60)
    print(f"📅 Shutdown Time: {current_time}")
    print("🔄 Gracefully stopping bot...")
    print("💾 Cleaning up resources...")

    # Clean up active countdowns
    if active_countdowns:
        print(f"🧹 Cleaning up {len(active_countdowns)} active countdown(s)")
        active_countdowns.clear()

    # Cancel background tasks
    if hasattr(bot, 'countdown_task') and not bot.countdown_task.done():
        bot.countdown_task.cancel()
        print("⏹️ Countdown task cancelled")

    print("=" * 60)
    print("✅ BOT SHUTDOWN COMPLETE - GOODBYE!")
    print("=" * 60)

    # Exit gracefully
    sys.exit(0)

# Run the bot
if __name__ == "__main__":
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

    try:
        print("🚀 Starting Guild Wars 2 World Boss Bot...")
        print("⚡ Press Ctrl+C to stop the bot gracefully")
        print("-" * 60)

        # Get token and validate
        token = os.getenv('DISCORD_TOKEN')
        if not token:
            print("❌ ERROR: DISCORD_TOKEN not found in environment variables!")
            print("💡 Make sure your .env file contains: DISCORD_TOKEN=your_bot_token")
            sys.exit(1)

        # Run the bot
        bot.run(token)

    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S IRST")
        print("\n" + "=" * 60)
        print("💥 UNEXPECTED ERROR OCCURRED")
        print("=" * 60)
        print(f"📅 Error Time: {current_time}")
        print(f"❌ Error: {e}")
        print("🔄 Bot will attempt to restart...")
        print("=" * 60)
        sys.exit(1)
